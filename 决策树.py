"""
决策树优化特征区间提取脚本 - 多分类升级版

功能说明：
- 支持多分类目标变量（不限于二分类）
- 可配置多种类别比值组合（如类别1比类别0、类别2比类别1等）
- 自动寻找最优特征区间以最大化指定类别的比值

使用方法：
1. 修改 TARGET_COLUMN 为你的目标列名
2. 修改 ALLOWED_TARGET_VALUES 为你数据中的实际类别值
3. 配置 TARGET_RATIO_CONFIGS 设置你想要的类别比值组合
4. 修改 file_path 为你的数据文件路径
5. 运行脚本

示例配置：
- 如果你的目标变量有类别 [0, 1, 2, 3]，想要分析类别3相对于类别0的比值：
  在 TARGET_RATIO_CONFIGS 中添加：
  {
      'name': '类别3比类别0',
      'numerator_class': 3,
      'denominator_class': 0,
      'min_purity': 0.15,
      'description': '寻找类别3相对于类别0的高比值区间'
  }
"""

import pandas as pd
import numpy as np
import time
import json # 仅用于调试或非常特殊情况，不再用于主要输出
import traceback
import warnings

# 忽略 pandas 的 SettingWithCopyWarning
#warnings.filterwarnings('ignore', category=pd.core.common.SettingWithCopyWarning)

# --- 配置参数 (升级版：支持多分类) ---
TARGET_COLUMN = 'aath'
CATEGORICAL_COLUMNS = ['网站_编码', 'Twitter_编码', 'Telegram_编码','时间段','描述']

# 排除不参与计算的列
excluded_columns = ['token_address','athh', '值定曲线', '检测时间','s77','high_bf_atl','atl_bf_ath','网站','launch_time', 'Twitter', 'Telegram','代币地址','ath_time_diff_seconds', 'atl_time_diff_seconds','创建者' ,'名称' ,'符号' ,'added_time' ,'atl_time' ,'ath_time' ,'ath比值' ,'ath' ,'特征' ,'详情','账号创建时间',  'website', '账号推文分析', '标签', '推特caller', 'TGCaller']

# --- 多分类目标配置 ---
# 支持的目标变量值（可以是二分类或多分类）
ALLOWED_TARGET_VALUES = [0, 1, 2]  # 支持0, 1, 2三个类别

# 目标类别比值配置 - 可以设置多种比值组合
# 用户可以根据需要修改以下配置：
# - 添加更多类别比值组合（如类别3比类别0等）
# - 调整min_purity阈值（建议范围：0.05-0.30）
# - 修改类别值以匹配实际数据
TARGET_RATIO_CONFIGS = [
    {
        'name': '类别1比类别0',
        'numerator_class': 1,
        'denominator_class': 0,
        'min_purity': 0.1,
        'description': '寻找类别1相对于类别0的高比值区间'
    }
    # 如果需要其他比值配置，可以在这里添加：
    # {
    #     'name': '类别1比类别0',
    #     'numerator_class': 1,
    #     'denominator_class': 0,
    #     'min_purity': 0.10,
    #     'description': '寻找类别1相对于类别0的高比值区间'
    # },
    # {
    #     'name': '类别2比类别1',
    #     'numerator_class': 2,
    #     'denominator_class': 1,
    #     'min_purity': 0.10,
    #     'description': '寻找类别2相对于类别1的高比值区间'
    # }
]

# 其他参数
MAX_ITERATIONS = 66666 # 内部搜索迭代次数
NUMERICAL_SHRINK_STEPS = 6666
RANDOM_STATE = 42

np.random.seed(RANDOM_STATE)

# --- 类型转换函数 (仅用于格式化数字，而不是JSON序列化) ---
def format_value_for_print(obj):
    if isinstance(obj, np.bool_):
        return bool(obj)
    if isinstance(obj, np.integer):
        return int(obj)
    if isinstance(obj, np.floating):
        if np.isinf(obj):
            return "Infinity" if obj > 0 else "-Infinity"
        if np.isnan(obj):
            return "NaN"
        return f"{float(obj):.4f}" # 保留4位小数
    if isinstance(obj, float): # 处理Python原生的float
        if obj == float('inf'):
            return "Infinity"
        if obj == float('-inf'):
            return "-Infinity"
        if obj != obj: # Check for NaN
            return "NaN"
        return f"{obj:.4f}" # 保留4位小数
    if isinstance(obj, (set, list, tuple)):
        return [format_value_for_print(el) for el in obj]
    if isinstance(obj, (bool, int, str)) or obj is None:
        return obj
    return str(obj) # Fallback for other types


# --- 数据预处理函数 (简化版：只做必要的numpy转换) ---
def preprocess_data_for_optimization(df, numerical_features, categorical_features, target_col_name):
    """
    预处理数据，将所有特征转换为numpy数组以提高性能
    """
    data_cache = {}

    # 预处理数值特征
    for feat in numerical_features:
        # 处理NaN值，用0填充
        data_cache[feat] = df[feat].fillna(0).to_numpy()

    # 预处理分类特征
    for feat in categorical_features:
        # 处理NaN值，用-1填充
        data_cache[feat] = df[feat].fillna(-1).to_numpy()

    # 预处理目标列
    data_cache[target_col_name] = df[target_col_name].to_numpy()

    return data_cache







# --- 增量掩码计算优化类 ---
class IncrementalMaskCalculator:
    """
    增量掩码计算器，用于优化特征区间评估的性能
    通过缓存和复用中间计算结果来避免重复计算
    """
    def __init__(self, data_cache, data_length):
        self.data_cache = data_cache
        self.data_length = data_length
        # 缓存每个特征的掩码计算结果
        self.feature_mask_cache = {}

    def get_feature_mask(self, feature, setting, numerical_features, categorical_features_config):
        """
        获取单个特征的掩码，使用缓存机制避免重复计算
        """
        # 创建缓存键
        if feature in categorical_features_config:
            if setting and isinstance(setting, (set, list, tuple)) and len(setting) > 0:
                cache_key = (feature, 'cat', tuple(sorted(setting)))
            else:
                return np.ones(self.data_length, dtype=bool)
        elif feature in numerical_features:
            min_val, max_val = setting
            cache_key = (feature, 'num', min_val, max_val)
        else:
            return np.ones(self.data_length, dtype=bool)

        # 检查缓存
        if cache_key in self.feature_mask_cache:
            return self.feature_mask_cache[cache_key]

        # 计算掩码
        if feature in categorical_features_config:
            feature_values = self.data_cache[feature]
            mask = np.isin(feature_values, list(setting))
        elif feature in numerical_features:
            feature_values = self.data_cache[feature]
            mask = np.ones(self.data_length, dtype=bool)
            if min_val is not None and min_val > -np.inf:
                mask &= (feature_values >= min_val)
            if max_val is not None and max_val < np.inf:
                mask &= (feature_values <= max_val)

        # 缓存结果
        self.feature_mask_cache[cache_key] = mask
        return mask

    def clear_cache(self):
        """清理缓存以释放内存"""
        self.feature_mask_cache.clear()

# --- 边界值预筛选优化类 ---
class BoundaryPrefilter:
    """
    边界值预筛选器，用于智能选择候选边界值
    基于数据分布和信息增益来减少无效搜索
    """
    def __init__(self, data_cache, target_col_name, ratio_config):
        self.data_cache = data_cache
        self.target_col_name = target_col_name
        self.ratio_config = ratio_config
        self.boundary_cache = {}

    def get_smart_boundaries(self, feature, df, max_boundaries=None):
        """
        获取智能筛选后的边界值

        Args:
            feature: 特征名
            df: 原始数据框（用于获取唯一值）
            max_boundaries: 最大边界数量，如果为None则使用NUMERICAL_SHRINK_STEPS

        Returns:
            list: 筛选后的边界值列表
        """
        if max_boundaries is None:
            max_boundaries = NUMERICAL_SHRINK_STEPS

        # 检查缓存
        cache_key = (feature, max_boundaries)
        if cache_key in self.boundary_cache:
            return self.boundary_cache[cache_key]

        # 获取所有唯一值并排序
        unique_sorted_values = sorted(df[feature].dropna().unique())

        if len(unique_sorted_values) <= max_boundaries:
            # 如果唯一值数量不多，直接返回所有值
            self.boundary_cache[cache_key] = unique_sorted_values
            return unique_sorted_values

        # 使用智能筛选策略
        selected_boundaries = self._select_informative_boundaries(
            feature, unique_sorted_values, max_boundaries
        )

        self.boundary_cache[cache_key] = selected_boundaries
        return selected_boundaries

    def _select_informative_boundaries(self, feature, unique_values, max_boundaries):
        """
        基于信息增益选择最有价值的边界值
        """
        if len(unique_values) <= max_boundaries:
            return unique_values

        # 获取特征数据和目标数据
        feature_data = self.data_cache[feature]
        target_data = self.data_cache[self.target_col_name]

        # 计算每个潜在分割点的信息增益
        boundary_scores = []

        # 为了性能考虑，我们采样一些候选分割点
        n_candidates = min(len(unique_values) - 1, max_boundaries * 3)
        candidate_indices = np.linspace(0, len(unique_values) - 2, n_candidates, dtype=int)

        for i in candidate_indices:
            split_value = (unique_values[i] + unique_values[i + 1]) / 2

            # 计算分割后的信息增益
            left_mask = feature_data <= split_value
            right_mask = ~left_mask

            if not left_mask.any() or not right_mask.any():
                continue

            # 计算信息增益（简化版本，基于目标类别分布）
            score = self._calculate_split_score(target_data, left_mask, right_mask)
            boundary_scores.append((score, unique_values[i + 1]))

        # 按分数排序并选择最佳边界
        boundary_scores.sort(reverse=True)

        # 选择前max_boundaries个最佳边界，并确保包含极值
        selected = [unique_values[0]]  # 包含最小值

        # 添加高分边界
        for score, boundary in boundary_scores[:max_boundaries-2]:
            if boundary not in selected:
                selected.append(boundary)

        selected.append(unique_values[-1])  # 包含最大值

        return sorted(selected)

    def _calculate_split_score(self, target_data, left_mask, right_mask):
        """
        计算分割的评分（基于目标类别的分布改善）
        """
        numerator_class = self.ratio_config['numerator_class']
        denominator_class = self.ratio_config['denominator_class']

        # 计算左右分支的目标类别比例
        left_targets = target_data[left_mask]
        right_targets = target_data[right_mask]

        if len(left_targets) == 0 or len(right_targets) == 0:
            return 0.0

        # 计算左分支的比值
        left_num = np.sum(left_targets == numerator_class)
        left_denom = np.sum(left_targets == denominator_class)
        left_ratio = left_num / left_denom if left_denom > 0 else 0

        # 计算右分支的比值
        right_num = np.sum(right_targets == numerator_class)
        right_denom = np.sum(right_targets == denominator_class)
        right_ratio = right_num / right_denom if right_denom > 0 else 0

        # 评分基于比值的差异（分离度）
        score = abs(left_ratio - right_ratio)

        # 考虑样本数量的权重
        total_samples = len(left_targets) + len(right_targets)
        left_weight = len(left_targets) / total_samples
        right_weight = len(right_targets) / total_samples

        # 平衡的分割获得更高分数
        balance_bonus = 1.0 - abs(left_weight - right_weight)

        return score * balance_bonus

    def clear_cache(self):
        """清理缓存以释放内存"""
        self.boundary_cache.clear()

# --- 核心评估函数 (增量掩码优化版：支持多分类比值计算) ---
def evaluate_intervals_optimized(intervals, data_cache, numerical_features, categorical_features_config, target_col_name, current_min_absolute_coverage, data_length, ratio_config, mask_calculator=None):
    """
    评估特征区间的性能，支持多分类目标变量，使用增量掩码计算优化

    Args:
        intervals: 特征区间字典
        data_cache: 预处理的数据缓存
        numerical_features: 数值特征列表
        categorical_features_config: 分类特征列表
        target_col_name: 目标列名
        current_min_absolute_coverage: 最小覆盖数量
        data_length: 数据长度
        ratio_config: 比值配置字典，包含numerator_class, denominator_class, min_purity
        mask_calculator: 增量掩码计算器实例

    Returns:
        tuple: (分子类别数量, 分母类别数量, 分子类别纯度, 比值, 是否满足约束, 得分)
    """
    # 如果没有提供掩码计算器，使用原始方法
    if mask_calculator is None:
        current_selection_mask = np.ones(data_length, dtype=bool)
        for feature, setting in intervals.items():
            if not current_selection_mask.any():
                break

            if feature in categorical_features_config:
                if setting and isinstance(setting, (set, list, tuple)) and len(setting) > 0:
                    feature_values = data_cache[feature]
                    current_selection_mask &= np.isin(feature_values, list(setting))
            elif feature in numerical_features:
                min_val, max_val = setting
                feature_values = data_cache[feature]
                if min_val is not None and min_val > -np.inf:
                    current_selection_mask &= (feature_values >= min_val)
                if max_val is not None and max_val < np.inf:
                    if not current_selection_mask.any(): continue
                    current_selection_mask &= (feature_values <= max_val)
    else:
        # 使用增量掩码计算器优化版本
        current_selection_mask = np.ones(data_length, dtype=bool)
        for feature, setting in intervals.items():
            if not current_selection_mask.any():
                break
            feature_mask = mask_calculator.get_feature_mask(feature, setting, numerical_features, categorical_features_config)
            current_selection_mask &= feature_mask

    if not current_selection_mask.any():
        return 0, 0, 0.0, 0.0, False, -float('inf')

    # 使用预处理的目标列数据
    target_values_in_subset = data_cache[target_col_name][current_selection_mask]

    if target_values_in_subset.size == 0:
        return 0, 0, 0.0, 0.0, False, -float('inf')

    # 获取比值配置
    numerator_class = ratio_config['numerator_class']
    denominator_class = ratio_config['denominator_class']
    min_purity = ratio_config['min_purity']

    # 计算各类别的数量
    n_total_in_subset = len(target_values_in_subset)
    n_numerator = np.sum(target_values_in_subset == numerator_class)
    n_denominator = np.sum(target_values_in_subset == denominator_class)

    # 计算分子类别的纯度
    purity_of_numerator = n_numerator / n_total_in_subset if n_total_in_subset > 0 else 0.0

    # 计算比值
    ratio_num_to_denom = n_numerator / n_denominator if n_denominator > 0 else float('inf')

    # 检查是否满足硬约束条件
    meets_hard_constraints = (purity_of_numerator >= min_purity and
                              n_numerator >= current_min_absolute_coverage)

    score = -float('inf')
    if meets_hard_constraints:
        score = ratio_num_to_denom

    return n_numerator, n_denominator, purity_of_numerator, ratio_num_to_denom, meets_hard_constraints, score


# --- 主搜索逻辑 (升级版本：支持多分类比值配置) ---
def heuristic_interval_search(df, current_min_absolute_coverage, ratio_config):
    start_time = time.time()
    config_name = ratio_config['name']
    numerator_class = ratio_config['numerator_class']
    denominator_class = ratio_config['denominator_class']
    # 减少详细输出，只在需要时显示

    all_cols = df.columns.tolist()

    # 定义所有不作为特征的列
    all_non_feature_columns = [TARGET_COLUMN] + excluded_columns

    # 数值特征是 DataFrame 中所有列减去目标列、排除列和分类特征列
    potential_numerical_features = [col for col in all_cols
                                   if col not in all_non_feature_columns and col not in CATEGORICAL_COLUMNS]

    # 检查并过滤真正的数值特征
    numerical_features = []
    skipped_features = []
    failed_features = []

    for col in potential_numerical_features:
        try:
            # 尝试转换为数值类型
            numeric_data = pd.to_numeric(df[col], errors='coerce')
            # 如果转换后的非空值比例大于45%，认为是数值特征
            valid_ratio = (~numeric_data.isna()).sum() / len(numeric_data)
            if valid_ratio > 0.45:
                numerical_features.append(col)
                # 更新DataFrame中的数据为数值类型
                df[col] = numeric_data
            else:
                skipped_features.append(f"{col}({valid_ratio:.2f})")
        except Exception as e:
            failed_features.append(col)

    print(f"数值特征处理完成: 有效特征{len(numerical_features)}个", end="")
    if skipped_features:
        print(f", 跳过{len(skipped_features)}个(比例不足)", end="")
    if failed_features:
        print(f", 失败{len(failed_features)}个", end="")
    print()

    # 调试输出：显示被跳过的具体列名和比例
    if skipped_features:
        print("被跳过的列详情(有效数值比例≤46%):")
        for skipped in skipped_features:
            print(f"  - {skipped}")
    if failed_features:
        print("处理失败的列:")
        for failed in failed_features:
            print(f"  - {failed}")

    # 确保分类特征只包含存在于DataFrame中的列
    existing_categorical_columns = [col for col in CATEGORICAL_COLUMNS if col in df.columns]
    missing_categorical = [col for col in CATEGORICAL_COLUMNS if col not in df.columns]

    if not existing_categorical_columns and not numerical_features :
        print("错误：没有定义任何有效的特征列 (数值或分类)。")
        return None

    if missing_categorical:
        print(f"分类特征处理: 有效{len(existing_categorical_columns)}个, 缺失{len(missing_categorical)}个")

    # 简单优化：预处理数据为numpy数组
    data_cache = preprocess_data_for_optimization(df, numerical_features, existing_categorical_columns, TARGET_COLUMN)
    data_length = len(df)

    # 创建增量掩码计算器和边界值预筛选器
    mask_calculator = IncrementalMaskCalculator(data_cache, data_length)
    boundary_prefilter = BoundaryPrefilter(data_cache, TARGET_COLUMN, ratio_config)

    initial_intervals = {}
    for feat in numerical_features:
        initial_intervals[feat] = [-np.inf, np.inf]
    for cat_feat in existing_categorical_columns:
        initial_intervals[cat_feat] = set(df[cat_feat].unique())

    # 使用优化后的评估函数
    (n_numerator_init, n_denominator_init, pur_init, rat_init, meets_init, current_score) = evaluate_intervals_optimized(
        initial_intervals, data_cache, numerical_features, existing_categorical_columns, TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
    )

    # 添加调试信息
    print(f"初始检查: aath=1数量={n_numerator_init}, aath=0数量={n_denominator_init}, 纯度={pur_init:.4f}, 比值={rat_init:.4f}, 满足约束={meets_init}")
    print(f"约束要求: 最小纯度>={ratio_config['min_purity']}, 最小覆盖>={current_min_absolute_coverage}")


    current_intervals = initial_intervals.copy()
    best_intervals_ever = None
    best_score_ever = -float('inf')
    best_stats_ever = None

    if meets_init:
        best_intervals_ever = initial_intervals.copy()
        best_score_ever = current_score
        best_stats_ever = (n_numerator_init, n_denominator_init, pur_init, rat_init, meets_init)
    else:
        # 如果初始状态不满足约束，尝试从更小的区间开始搜索
        print("初始状态不满足约束，尝试从小区间开始搜索...")
        # 尝试每个特征的高价值区间
        for feat in numerical_features[:5]:  # 只尝试前5个特征以节省时间
            unique_vals = sorted(df[feat].dropna().unique())
            if len(unique_vals) < 10:
                continue
            # 尝试上四分位数区间
            q75 = df[feat].quantile(0.75)
            q95 = df[feat].quantile(0.95)
            test_intervals = initial_intervals.copy()
            test_intervals[feat] = [q75, q95]

            test_result = evaluate_intervals_optimized(
                test_intervals, data_cache, numerical_features, existing_categorical_columns,
                TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
            )
            if test_result[4]:  # meets_constraints
                best_intervals_ever = test_intervals.copy()
                best_score_ever = test_result[5]
                best_stats_ever = test_result[:5]
                print(f"找到满足约束的初始区间: {feat} in [{q75:.4f}, {q95:.4f}]")
                break


    for iteration in range(MAX_ITERATIONS):
        # print(f"\n--- 迭代 {iteration + 1}/{MAX_ITERATIONS} (MIN_ABSOLUTE_COVERAGE={current_min_absolute_coverage}) ---")
        made_improvement_in_iteration = False
        candidate_next_intervals_for_iter = current_intervals.copy()
        best_score_this_iter = current_score

        features_to_try = numerical_features + [col for col in existing_categorical_columns if col in current_intervals]
        np.random.shuffle(features_to_try)

        for feature_to_modify in features_to_try:
            temp_intervals = current_intervals.copy()

            if feature_to_modify in existing_categorical_columns:
                original_cat_setting = temp_intervals[feature_to_modify].copy()
                if not original_cat_setting : continue

                for cat_to_remove in list(original_cat_setting):
                    eval_temp_intervals = temp_intervals.copy()
                    eval_temp_intervals[feature_to_modify] = original_cat_setting - {cat_to_remove}

                    _, _, _, _, _, new_score = evaluate_intervals_optimized(
                        eval_temp_intervals, data_cache, numerical_features, existing_categorical_columns, TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
                    )
                    if new_score > best_score_this_iter:
                        best_score_this_iter = new_score
                        candidate_next_intervals_for_iter = eval_temp_intervals.copy()
                        made_improvement_in_iteration = True

            elif feature_to_modify in numerical_features:
                original_num_setting = list(temp_intervals[feature_to_modify])

                # 使用边界值预筛选器获取智能筛选的边界
                potential_boundaries = boundary_prefilter.get_smart_boundaries(feature_to_modify, df)
                if len(potential_boundaries) < 2: continue

                current_lower, current_upper = original_num_setting

                for p_bound in sorted(potential_boundaries):
                    if p_bound > current_lower and (current_upper is np.inf or p_bound < current_upper):
                        eval_temp_intervals = temp_intervals.copy()
                        eval_temp_intervals[feature_to_modify] = [p_bound, current_upper]
                        _, _, _, _, _, new_score = evaluate_intervals_optimized(
                            eval_temp_intervals, data_cache, numerical_features, existing_categorical_columns, TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
                        )
                        if new_score > best_score_this_iter:
                            best_score_this_iter = new_score
                            candidate_next_intervals_for_iter = eval_temp_intervals.copy()
                            made_improvement_in_iteration = True

                for p_bound in sorted(potential_boundaries, reverse=True):
                    if p_bound < current_upper and (current_lower is -np.inf or p_bound > current_lower):
                        eval_temp_intervals = temp_intervals.copy()
                        eval_temp_intervals[feature_to_modify] = [current_lower, p_bound]
                        _, _, _, _, _, new_score = evaluate_intervals_optimized(
                            eval_temp_intervals, data_cache, numerical_features, existing_categorical_columns, TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
                        )
                        if new_score > best_score_this_iter:
                            best_score_this_iter = new_score
                            candidate_next_intervals_for_iter = eval_temp_intervals.copy()
                            made_improvement_in_iteration = True

        if made_improvement_in_iteration:
            current_intervals = candidate_next_intervals_for_iter
            current_score = best_score_this_iter

            n_numerator, n_denominator, pur, rat, meets, _ = evaluate_intervals_optimized(
                current_intervals, data_cache, numerical_features, existing_categorical_columns, TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
            )

            if meets and current_score > best_score_ever:
                best_score_ever = current_score
                best_intervals_ever = current_intervals.copy()
                best_stats_ever = (n_numerator, n_denominator, pur, rat, meets)
        else:
            break

    # 清理缓存以释放内存
    mask_calculator.clear_cache()
    boundary_prefilter.clear_cache()

    end_time = time.time()
    # 减少详细输出，只显示关键信息

    if best_intervals_ever:
        # 获取最终统计数据，确保与最终的 best_intervals_ever 一致
        n_numerator, n_denominator, pur, rat, meets, _ = evaluate_intervals_optimized(
            best_intervals_ever, data_cache, numerical_features, existing_categorical_columns, TARGET_COLUMN, current_min_absolute_coverage, data_length, ratio_config, mask_calculator
        )
        final_stats = (n_numerator, n_denominator, pur, rat, meets)

        result_entry = {
            "min_absolute_coverage_param": current_min_absolute_coverage,
            "ratio_config": ratio_config,
            "score": best_score_ever,
            "intervals": best_intervals_ever,
            "performance": {
                'n_numerator_covered': final_stats[0],
                'n_denominator_covered': final_stats[1],
                'purity_of_numerator': final_stats[2],
                'ratio_numerator_to_denominator': final_stats[3],
                'meets_constraints': final_stats[4]
            }
        }
        return result_entry
    else:
        return None

# --- 主程序入口 (升级版：支持多分类比值配置) -----------------------------------------------------------------------------------------------------------------------------------------------------------
# --- 主程序入口 (升级版：支持多分类比值配置) -----------------------------------------------------------------------------------------------------------------------------------------------------------
# --- 主程序入口 (升级版：支持多分类比值配置) -----------------------------------------------------------------------------------------------------------------------------------------------------------
# --- 主程序入口 (升级版：支持多分类比值配置) -----------------------------------------------------------------------------------------------------------------------------------------------------------
# --- 主程序入口 (升级版：支持多分类比值配置) -----------------------------------------------------------------------------------------------------------------------------------------------------------
# --- 主程序入口 (升级版：支持多分类比值配置) -----------------------------------------------------------------------------------------------------------------------------------------------------------

file_path = '/content/test00.xlsx'  # 使用相对路径

# 定义 MIN_ABSOLUTE_COVERAGE 的搜索范围
MIN_COVERAGE_START = 460
MIN_COVERAGE_END = 477 # 包含50

all_results = []

try:
    print(f"正在加载数据文件: {file_path}")

    # 智能文件格式检测和读取
    if file_path.lower().endswith(('.xlsx', '.xls')):
        print("检测到Excel文件，使用pandas.read_excel()读取...")
        df_main = pd.read_excel(file_path)
    elif file_path.lower().endswith('.csv'):
        print("检测到CSV文件，使用pandas.read_csv()读取...")
        # 尝试不同的编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
        df_main = None
        for encoding in encodings:
            try:
                df_main = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取CSV文件")
                break
            except UnicodeDecodeError:
                continue
        if df_main is None:
            raise ValueError("无法使用任何编码格式读取CSV文件")
    else:
        raise ValueError(f"不支持的文件格式: {file_path}。请使用 .csv, .xlsx 或 .xls 文件")

    df_main.columns = df_main.columns.str.strip()
    print(f"数据加载完成: {df_main.shape[0]}行 x {df_main.shape[1]}列")

    if TARGET_COLUMN not in df_main.columns:
        raise KeyError(f"错误：目标列 '{TARGET_COLUMN}' 不在文件的列中。可用列: {df_main.columns.tolist()}")

    # 检查目标列的值
    unique_targets = df_main[TARGET_COLUMN].unique()
    print(f"目标列 '{TARGET_COLUMN}' 的唯一值: {unique_targets}")

    # 验证目标列的值是否在允许的范围内
    if not set(unique_targets).issubset(set(ALLOWED_TARGET_VALUES)):
        raise ValueError(f"目标列 '{TARGET_COLUMN}' 包含不支持的值。当前值: {unique_targets}, 允许的值: {ALLOWED_TARGET_VALUES}")

    # 验证配置的比值类别是否存在于数据中
    invalid_configs = []
    for config in TARGET_RATIO_CONFIGS:
        numerator_class = config['numerator_class']
        denominator_class = config['denominator_class']
        if numerator_class not in unique_targets or denominator_class not in unique_targets:
            invalid_configs.append(config['name'])

    if invalid_configs:
        print(f"警告: {len(invalid_configs)}个配置因类别不存在将被跳过")

    # 遍历所有比值配置和覆盖度参数
    for ratio_config in TARGET_RATIO_CONFIGS:
        # 检查配置的类别是否存在于数据中
        numerator_class = ratio_config['numerator_class']
        denominator_class = ratio_config['denominator_class']
        if numerator_class not in unique_targets or denominator_class not in unique_targets:
            continue

        print(f"\n{'='*60}")
        print(f"处理配置: {ratio_config['name']} (类别{numerator_class}比{denominator_class})")
        print(f"{'='*60}")

        coverage_range = range(MIN_COVERAGE_START, MIN_COVERAGE_END + 1)
        print(f"开始搜索 (覆盖度范围: {MIN_COVERAGE_START}-{MIN_COVERAGE_END})")

        for current_coverage in coverage_range:
            print(f"  覆盖度{current_coverage}...", end=" ")
            # 性能优化：避免不必要的数据复制，直接传入原始数据框
            # heuristic_interval_search 函数内部已经有适当的数据处理机制
            result = heuristic_interval_search(df_main, current_coverage, ratio_config)
            if result:
                all_results.append(result)
                print("✓")
            else:
                print("✗")
        print(f"配置 {ratio_config['name']} 完成")

    if all_results:
        print("\n\n--- 所有结果集合排序和选择前7个 ---")

        # 按比值配置分组结果
        results_by_config = {}
        for result in all_results:
            config_name = result['ratio_config']['name']
            if config_name not in results_by_config:
                results_by_config[config_name] = []
            results_by_config[config_name].append(result)

        # 为每个配置显示最佳结果
        for config_name, config_results in results_by_config.items():
            print(f"\n{'='*60}")
            print(f"比值配置: {config_name}")
            print(f"{'='*60}")

            # 分离出 score 为 'Infinity' 的结果
            infinity_results = [r for r in config_results if r['score'] == float('inf')]
            # 过滤掉非数字或负无穷的结果，只对有效的数字分数进行排序
            sortable_results = [r for r in config_results if r['score'] != float('inf') and r['score'] != -float('inf') and not np.isnan(r['score'])]

            # 将可排序结果按 score 降序排列
            sorted_results = sorted(sortable_results, key=lambda x: x['score'], reverse=True)

            # 将 Infinity 的结果放在最前面，然后是排序后的结果
            final_sorted_results = infinity_results + sorted_results

            # 选出前7个最佳结果 (如果存在)
            top_7_results = final_sorted_results[:7]

            print(f"找到 {len(final_sorted_results)} 个满足约束的区间集合。")
            print(f"最佳 {min(len(top_7_results), 7)} 个特征区间集合:")

            for i, res in enumerate(top_7_results):
                ratio_config = res['ratio_config']
                numerator_class = ratio_config['numerator_class']
                denominator_class = ratio_config['denominator_class']

                print(f"\n--- 排名 {i+1} (MIN_ABSOLUTE_COVERAGE={res['min_absolute_coverage_param']}) ---")

                # 直接打印结果的性能统计
                perf = res['performance']
                print(f"  覆盖的 '{TARGET_COLUMN}={numerator_class}' 样本数量: {perf['n_numerator_covered']}")
                print(f"  覆盖的 '{TARGET_COLUMN}={denominator_class}' 样本数量: {perf['n_denominator_covered']}")
                print(f"  '{TARGET_COLUMN}={numerator_class}' 的纯度: {perf['purity_of_numerator']:.4f}")
                ratio_str = format_value_for_print(perf['ratio_numerator_to_denominator'])
                print(f"  '{TARGET_COLUMN}={numerator_class}' 与 '{TARGET_COLUMN}={denominator_class}' 的数量比值: {ratio_str}")
                print("  特征区间:")

                # 遍历并打印特征区间
                all_cols_in_df = df_main.columns.tolist()
                # 重新计算 numerical_features_in_df 和 existing_categorical_features_in_df
                # 以确保它们与 heuristic_interval_search 函数内部的逻辑一致
                all_non_feature_columns_for_print = [TARGET_COLUMN] + excluded_columns
                numerical_features_in_df = [col for col in all_cols_in_df
                                            if col not in all_non_feature_columns_for_print and col not in CATEGORICAL_COLUMNS]
                existing_categorical_features_in_df = [col for col in CATEGORICAL_COLUMNS if col in df_main.columns]

                for feature_name, setting in res['intervals'].items():
                    if feature_name in existing_categorical_features_in_df:
                        # 分类特征 - 只有当类别被筛选过（不是全部原始类别）时才打印
                        original_categories = set(df_main[feature_name].unique())
                        current_categories = set(setting) if isinstance(setting, (set, list, tuple)) else {setting}

                        # 只有当当前类别少于原始类别时才打印（说明被优化过）
                        if len(current_categories) < len(original_categories):
                            formatted_categories = ", ".join([str(format_value_for_print(c)) for c in sorted(list(current_categories))])
                            print(f"    - {feature_name}: IN {{{formatted_categories}}}") # 用大括号括起来更像集合
                    elif feature_name in numerical_features_in_df:
                        # 数值特征
                        min_val, max_val = setting
                        print_parts = []
                        # format_value_for_print 现在返回的是字符串，直接使用
                        formatted_min = format_value_for_print(min_val)
                        formatted_max = format_value_for_print(max_val)

                        # 检查是否是负无穷或None，如果是，则不打印下限
                        if formatted_min != '-Infinity' and formatted_min is not None:
                            print_parts.append(f"{formatted_min} <=")

                        print_parts.append(feature_name)

                        # 检查是否是正无穷或None，如果是，则不打印上限
                        if formatted_max != 'Infinity' and formatted_max is not None:
                            print_parts.append(f"<= {formatted_max}")

                        # 只有当特征有实际的区间限制时才打印（即不是全范围）
                        # 如果是全范围（即min和max都为None/Inf），则不打印
                        # 注意：format_value_for_print会把 -np.inf 和 np.inf 转成字符串
                        # 所以这里需要检查转换后的字符串
                        if not ((formatted_min == '-Infinity' or formatted_min is None) and \
                               (formatted_max == 'Infinity' or formatted_max is None)):
                            print(f"    - {' '.join(print_parts)}")
    else:
        print("\n未找到任何满足约束条件的特征区间集合。")

except FileNotFoundError:
    print(f"错误：找不到数据文件 '{file_path}'。请确保文件路径正确，并且Colab有权访问。")
except KeyError as e:
    print(f"列名错误: {e}。请检查CSV文件中的列名是否与代码中的配置（TARGET_COLUMN, CATEGORICAL_COLUMNS）一致。")
except ValueError as e:
    print(f"数据值错误: {e}")
except Exception as e:
    print(f"发生未预料的系统错误: {e}")
    traceback.print_exc()