#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速检测数据中的NaN值工具
用于快速定位可能导致决策树算法失败的NaN值问题
"""

import pandas as pd
import numpy as np

def check_nan_values(file_path):
    """
    检查Excel文件中的NaN值分布
    
    Args:
        file_path: Excel文件路径
    """
    print(f"📊 正在检查文件: {file_path}")
    
    try:
        # 读取数据
        df = pd.read_excel(file_path)
        print(f"✅ 数据加载成功: {df.shape[0]}行 × {df.shape[1]}列\n")
        
        # 1. 总体NaN统计
        total_cells = df.shape[0] * df.shape[1]
        total_nan = df.isnull().sum().sum()
        nan_percentage = (total_nan / total_cells) * 100
        
        print(f"📈 总体NaN统计:")
        print(f"   总单元格数: {total_cells:,}")
        print(f"   NaN单元格数: {total_nan:,}")
        print(f"   NaN比例: {nan_percentage:.2f}%\n")
        
        # 2. 按列统计NaN
        nan_stats = df.isnull().sum()
        nan_cols = nan_stats[nan_stats > 0].sort_values(ascending=False)
        
        if len(nan_cols) == 0:
            print("🎉 恭喜！数据中没有发现NaN值")
            return
        
        print(f"⚠️  发现 {len(nan_cols)} 个列包含NaN值:")
        print("=" * 60)
        
        for col, count in nan_cols.items():
            percentage = (count / len(df)) * 100
            severity = "🔴" if percentage > 50 else "🟡" if percentage > 10 else "🟢"
            print(f"{severity} {col}: {count:,}个NaN ({percentage:.1f}%)")
        
        # 3. 决策树相关列的NaN检查
        print(f"\n🎯 决策树算法相关列的NaN检查:")
        print("=" * 60)
        
        # 模拟决策树代码中的列分类
        excluded_columns = ['token_address','athh', '值定曲线', '检测时间','s77','high_bf_atl','atl_bf_ath','网站','launch_time', 'Twitter', 'Telegram','代币地址','ath_time_diff_seconds', 'atl_time_diff_seconds','创建者' ,'名称' ,'符号' ,'added_time' ,'atl_time' ,'ath_time' ,'ath比值' ,'ath' ,'特征' ,'详情','账号创建时间',  'website', '账号推文分析', '标签', '推特caller', 'TGCaller']
        categorical_columns = ['网站_编码', 'Twitter_编码', 'Telegram_编码','时间段','描述']
        target_column = 'aath'
        
        # 检查目标列
        target_nan = df[target_column].isnull().sum() if target_column in df.columns else 0
        if target_nan > 0:
            print(f"🔴 目标列 '{target_column}': {target_nan}个NaN - 这会导致严重问题！")
        else:
            print(f"✅ 目标列 '{target_column}': 无NaN值")
        
        # 检查分类特征列
        print(f"\n📂 分类特征列NaN情况:")
        for col in categorical_columns:
            if col in df.columns:
                nan_count = df[col].isnull().sum()
                if nan_count > 0:
                    pct = (nan_count / len(df)) * 100
                    print(f"   🟡 {col}: {nan_count}个NaN ({pct:.1f}%)")
                else:
                    print(f"   ✅ {col}: 无NaN值")
            else:
                print(f"   ❌ {col}: 列不存在")
        
        # 检查数值特征列
        print(f"\n🔢 数值特征列NaN情况:")
        all_non_feature_columns = [target_column] + excluded_columns
        potential_numerical_features = [col for col in df.columns 
                                       if col not in all_non_feature_columns and col not in categorical_columns]
        
        numerical_nan_count = 0
        for col in potential_numerical_features:
            try:
                numeric_data = pd.to_numeric(df[col], errors='coerce')
                valid_ratio = (~numeric_data.isna()).sum() / len(numeric_data)
                if valid_ratio > 0.45:  # 决策树代码中的阈值
                    nan_count = df[col].isnull().sum()
                    if nan_count > 0:
                        numerical_nan_count += 1
                        pct = (nan_count / len(df)) * 100
                        severity = "🔴" if pct > 50 else "🟡" if pct > 10 else "🟢"
                        print(f"   {severity} {col}: {nan_count}个NaN ({pct:.1f}%)")
            except:
                pass
        
        if numerical_nan_count == 0:
            print("   ✅ 所有有效数值特征列都无NaN值")
        
        # 4. 建议
        print(f"\n💡 建议:")
        print("=" * 60)
        if target_nan > 0:
            print("🔴 目标列有NaN值，必须先清理目标列！")
        
        high_nan_cols = [col for col, count in nan_cols.items() if (count/len(df)) > 0.5]
        if high_nan_cols:
            print(f"🟡 以下列NaN比例超过50%，建议考虑删除或特殊处理:")
            for col in high_nan_cols:
                print(f"   - {col}")
        
        moderate_nan_cols = [col for col, count in nan_cols.items() if 0.1 < (count/len(df)) <= 0.5]
        if moderate_nan_cols:
            print(f"🟢 以下列NaN比例适中(10%-50%)，可以用填充方式处理:")
            for col in moderate_nan_cols:
                print(f"   - {col}")
        
        print(f"\n✅ 当前决策树代码已经自动处理NaN值:")
        print("   - 数值特征: NaN → 0")
        print("   - 分类特征: NaN → -1")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    # 默认检查test00.xlsx
    file_path = "test00.xlsx"
    check_nan_values(file_path)
