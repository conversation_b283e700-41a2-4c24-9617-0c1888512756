-- CTE 1: Identify tokens completing bonding curve on Pump.fun (proxy for PumpSwap listing event)
WITH pumpswap_listing_event AS (
  SELECT
    block_time AS listing_block_time,
    -- block_slot AS listing_block_slot,
    account_arguments[3] AS token_address -- Verify: Assume argument 3 is the token mint address for this instruction
  FROM solana.instruction_calls
  WHERE
    executing_account = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P' -- Pump.fun program ID
    AND BYTEARRAY_SUBSTRING(data, 1, 8) = 0x9beae792ec9ea21e -- Instruction indicating curve completion / readiness for PumpSwap
    AND tx_success = TRUE
    AND cardinality(inner_instructions) > 0 -- Filter used in the market cap query, potentially indicates successful completion
    -- Define the time range for the tokens you want to analyze based on their listing time
    AND block_time BETWEEN DATE '2025-04-18' AND DATE '2025-05-26' -- Example: Analyze tokens listed since PumpSwap launch until today
),

-- CTE 2: Filter valid trades on PumpSwap for the identified tokens
valid_trades AS (
  SELECT
    t.token_bought_mint_address,
    COALESCE(t.token_bought_symbol, tf.symbol) AS token_bought_symbol,
    t.block_time,
    t.block_slot,
    t.amount_usd,
    t.token_bought_amount,
    t.amount_usd / NULLIF(t.token_bought_amount, 0) AS price
  FROM dex_solana.trades t
  JOIN pumpswap_listing_event ple ON t.token_bought_mint_address = ple.token_address
  LEFT JOIN tokens_solana.fungible tf ON t.token_bought_mint_address = tf.token_mint_address
  WHERE
    LOWER(t.project) = 'pumpswap'
    AND t.token_bought_amount > 0
    AND t.amount_usd > 0
    AND t.block_slot >= ple.listing_block_slot + 3 -- Start analysis slightly after listing
    AND t.token_bought_amount > 77777
    AND t.amount_usd > 7
),

-- CTE 3: Calculate rolling median price for outlier filtering
trades_with_median AS (
  SELECT
    *,
    approx_percentile(price, 0.5) OVER (
      PARTITION BY token_bought_mint_address ORDER BY block_slot ROWS BETWEEN 5 PRECEDING AND 1 PRECEDING
    ) AS prev_5_median_price
  FROM valid_trades WHERE price IS NOT NULL
),

-- CTE 4: Filter out outlier trades
filtered_trades AS (
  SELECT * FROM trades_with_median
  WHERE prev_5_median_price IS NULL OR price <= (prev_5_median_price * 1.27)
),

-- CTE 5: Calculate ATH price and filter by market cap
ath_prices AS (
  SELECT
    token_bought_mint_address, token_bought_symbol, MAX(price) AS ath
  FROM filtered_trades GROUP BY token_bought_mint_address, token_bought_symbol
  HAVING MAX(price) * 1000000 > {{min_mc}} AND MAX(price) * 1000000 < {{max_mc}}
),

-- CTE 6: Calculate price around listing time ("30 second" price)
a77s_price_calc AS (
  WITH trades_with_median_early AS (
    SELECT
      t.token_bought_mint_address, t.block_slot,
      t.amount_usd / NULLIF(t.token_bought_amount, 0) as price,
      approx_percentile(t.amount_usd / NULLIF(t.token_bought_amount, 0), 0.5) OVER (
        PARTITION BY t.token_bought_mint_address ORDER BY t.block_slot ROWS BETWEEN 3 PRECEDING AND 1 PRECEDING
      ) AS prev_5_median_price
    FROM dex_solana.trades t JOIN pumpswap_listing_event ple ON t.token_bought_mint_address = ple.token_address
    WHERE LOWER(t.project) = 'pumpswap'
      AND t.block_slot > ple.listing_block_slot AND t.block_slot <= ple.listing_block_slot + 77
      AND t.token_bought_mint_address = ple.token_address
      AND t.token_bought_amount > 77777 AND t.amount_usd > 7
      AND (t.amount_usd / NULLIF(t.token_bought_amount, 0)) IS NOT NULL
  )
  SELECT token_bought_mint_address, MAX(CASE WHEN prev_5_median_price IS NULL OR price <= (prev_5_median_price * 1.37) THEN price END) AS a77s_price
  FROM trades_with_median_early GROUP BY token_bought_mint_address
),

-- CTE 7: Find the exact time/slot of the ATH
ath_with_time AS (
  SELECT f.*, ROW_NUMBER() OVER (PARTITION BY f.token_bought_mint_address ORDER BY f.price DESC, f.block_slot ASC) AS rn
  FROM filtered_trades f JOIN ath_prices a ON f.token_bought_mint_address = a.token_bought_mint_address AND f.price = a.ath
),
filtered_ath AS ( SELECT * FROM ath_with_time WHERE rn = 1 ),

-- CTE 8: Find the ATL before ATH (after stabilization)
prior_atl_with_time AS (
  SELECT t.token_bought_mint_address, t.token_bought_symbol, MIN(t.price) AS atl_before_ath
  FROM filtered_trades t JOIN filtered_ath fa ON t.token_bought_mint_address = fa.token_bought_mint_address JOIN pumpswap_listing_event ple ON t.token_bought_mint_address = ple.token_address
  WHERE t.block_slot < fa.block_slot AND t.block_slot > ple.listing_block_slot + 77
  GROUP BY t.token_bought_mint_address, t.token_bought_symbol
),

-- CTE 9: Find the exact time/slot of the ATL before ATH
prior_atl_time AS (
  SELECT t.*, ROW_NUMBER() OVER (PARTITION BY t.token_bought_mint_address ORDER BY t.block_slot ASC) AS rn_atl
  FROM filtered_trades t JOIN prior_atl_with_time pa ON t.token_bought_mint_address = pa.token_bought_mint_address AND t.price = pa.atl_before_ath
  WHERE t.block_slot < (SELECT block_slot FROM filtered_ath fa_inner WHERE fa_inner.token_bought_mint_address = t.token_bought_mint_address)
),
first_prior_atl AS ( SELECT * FROM prior_atl_time WHERE rn_atl = 1 ),

-- CTE 10: Calculate overall ATL after stabilization (ATL2)
atl2in as (
  SELECT t.token_bought_mint_address, MIN(t.price) * 1000000 AS atl2_mc
  FROM filtered_trades t GROUP BY t.token_bought_mint_address
),

-- CTE 11: Find the highest price before the first ATL (after stabilization)
high_before_atl AS (
  SELECT t.token_bought_mint_address, t.token_bought_symbol, MAX(t.price) AS high_before_atl
  FROM filtered_trades t JOIN first_prior_atl fpa ON t.token_bought_mint_address = fpa.token_bought_mint_address JOIN pumpswap_listing_event ple ON t.token_bought_mint_address = ple.token_address
  WHERE t.block_slot < fpa.block_slot AND t.block_slot > ple.listing_block_slot + 77
  GROUP BY t.token_bought_mint_address, t.token_bought_symbol
),

/*-- *** NEW CTE 12: Calculate Buys during Bonding Curve phase ***
bonding_curve_buys_cte AS (
    SELECT
        b.account_mint,
        COUNT(DISTINCT b.call_tx_id) AS buys_count
    FROM pumpdotfun_solana.pump_call_buy AS b
    WHERE b.account_mint IN (SELECT token_address FROM pumpswap_listing_event) -- Only for tokens listed in our timeframe
    GROUP BY b.account_mint
),

-- *** NEW CTE 13: Calculate Sells during Bonding Curve phase ***
bonding_curve_sells_cte AS (
    SELECT
        s.account_mint,
        COUNT(DISTINCT s.call_tx_id) AS sells_count
    FROM pumpdotfun_solana.pump_call_sell AS s
    WHERE s.account_mint IN (SELECT token_address FROM pumpswap_listing_event) -- Only for tokens listed in our timeframe
    GROUP BY s.account_mint
)

-- Final SELECT: Combine all calculated metrics including bonding curve buys/sells */
SELECT
 -- COALESCE(tf.name, ap.token_bought_symbol, ple.token_address) as name,
 -- COALESCE(tf.symbol, ap.token_bought_symbol) as symbol,
  ple.token_address, -- Raw address
  -- Estimated Market Caps (Price * 1 Billion Supply)
  CAST(ac.a77s_price * 1000000.0 AS BIGINT) as s77,          -- Cast result to BIGINT
  CAST(hba.high_before_atl * 1000000.0 AS BIGINT) as high_bf_atl, -- Cast result to BIGINT
  CAST(fpa.price * 1000000.0 AS BIGINT) AS atl_bf_ath,       -- Cast result to BIGINT
  CAST(ap.ath * 1000000.0 AS BIGINT) as ath,
  
  -- Times and Token Info
  ple.listing_block_time as added_time,
  fpa.block_time AS atl_time,
  fa.block_time AS ath_time,

  -- *** ADDED: Bonding Curve Buy/Sell Counts ***
  -- COALESCE(bcb.buys_count, 0) AS buys,
  -- COALESCE(bcs.sells_count, 0) AS sells
  -- Overall ATL Market Cap
 -- al2.atl2_mc
  -- Token Address (clickable link to Solscan)
 -- CONCAT('<a href="https://solscan.io/token/', ple.token_address, '" target="_blank">', ple.token_address, '</a>') AS token_address_link,
  

FROM pumpswap_listing_event ple
-- Join metrics for DEX trading
LEFT JOIN a77s_price_calc ac ON ple.token_address = ac.token_bought_mint_address
LEFT JOIN ath_prices ap ON ple.token_address = ap.token_bought_mint_address
LEFT JOIN filtered_ath fa ON ap.token_bought_mint_address = fa.token_bought_mint_address
LEFT JOIN first_prior_atl fpa ON ap.token_bought_mint_address = fpa.token_bought_mint_address
LEFT JOIN high_before_atl hba ON ap.token_bought_mint_address = hba.token_bought_mint_address
LEFT JOIN tokens_solana.fungible tf ON ple.token_address = tf.token_mint_address
LEFT JOIN atl2in al2 ON ap.token_bought_mint_address = al2.token_bought_mint_address
-- *** ADDED: Join Bonding Curve Buy/Sell Counts ***
-- LEFT JOIN bonding_curve_buys_cte bcb ON ple.token_address = bcb.account_mint
-- LEFT JOIN bonding_curve_sells_cte bcs ON ple.token_address = bcs.account_mint

WHERE
  ap.ath IS NOT NULL -- Ensure the token met the ATH market cap criteria
ORDER BY
  ple.listing_block_time DESC -- Show newest listings first
;